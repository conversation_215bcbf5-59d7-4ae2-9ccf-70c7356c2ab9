import { tool } from "ai";
import { z } from "zod";

export const canvasTool = tool({
  description: `Create a rich canvas for three distinct purposes:

1. markdown — Long-form writing (essays, docs, knowledge bases). **Markdown is strictly for text; DO NOT embed Mermaid diagrams here.**
2. code — Interactive web projects written in HTML/CSS/JS (single-file demos, widgets, prototypes).
3. chart — Programmatic chart creation (e.g. Chart.js, ECharts, D3). Provide executable front-end code; the agent will render the chart inside a self-contained HTML document.

When to use this tool:
• The user asks to draft or heavily edit long documents.
• The user wants a live web preview, UI mock-up, or small app.
• The user needs data visualisation / charts.

Avoid using canvas for:
• Quick Q&A style responses.
• Mermaid diagrams (use a dedicated diagram tool instead).
• Tiny illustrative code snippets that don't need a live preview.`,

  parameters: z.object({
    type: z
      .enum(["markdown", "code", "chart"])
      .describe("Canvas mode: 'markdown', 'code', or 'chart'"),
    title: z
      .string()
      .describe("Short, descriptive title for the canvas content"),
    content: z
      .string()
      .describe(
        "Main body: markdown text, or full HTML/CSS/JS when type ≠ 'markdown'. For markdown canvases, provide the markdown content. For code canvases, provide HTML/CSS/JS. For chart canvases, you can leave this empty if providing chartSpec."
      ),
    language: z
      .string()
      .default("")
      .describe(
        "Programming language for 'code' canvas (html, css, js, etc.). Use empty string for markdown or chart canvases."
      ),
    chartSpec: z
      .string()
      .default("")
      .describe(
        "For chart canvases only: JSON or JS snippet describing the chart configuration. Use empty string for non-chart canvases."
      ),
    library: z
      .string()
      .default("")
      .describe(
        "For chart canvases only: Preferred JS charting library ('chartjs', 'echarts', or 'd3'). Use empty string for non-chart canvases."
      ),
  }),

  execute: async ({ type, title, content, language, chartSpec, library }) => {
    if (!type) {
      throw new Error("Type is required for canvas tool");
    }

    let finalContent = content || "";
    const finalLanguage = language || "";
    const finalChartSpec = chartSpec || "";

    // Handle library parameter - convert to proper enum value or null
    let finalLibrary: "chartjs" | "echarts" | "d3" | null = null;
    if (library && ["chartjs", "echarts", "d3"].includes(library)) {
      finalLibrary = library as "chartjs" | "echarts" | "d3";
    } else if (type === "chart") {
      finalLibrary = "chartjs"; // Default for chart type
    }

    if (type === "chart") {
      if (!finalChartSpec) {
        throw new Error("chartSpec is required when type is 'chart'");
      }

      // If content not provided, create a self-contained HTML using Chart.js (default) or specified library
      if (!finalContent) {
        if (finalLibrary === "chartjs") {
          finalContent = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>${title}</title>
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
  <canvas id="myChart"></canvas>
  <script>
    const ctx = document.getElementById('myChart').getContext('2d');
    const spec = ${finalChartSpec};
    new Chart(ctx, spec);
  </script>
</body>
</html>`;
        } else {
          // For echarts or d3, leave placeholder for user-provided code
          finalContent = `<pre style="color:red">Please provide 'content' with HTML/JS for library ${finalLibrary}.</pre>`;
        }
      }
    }

    return {
      type,
      title,
      content: finalContent || "",
      language: finalLanguage || undefined,
      chartSpec: finalChartSpec || undefined,
      library: finalLibrary || undefined,
      updatedAt: Date.now(),
    };
  },
});
