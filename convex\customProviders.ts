import { v } from "convex/values";
import { query, mutation, action } from "./_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";

export const list = query({
  args: {},
  returns: v.array(
    v.object({
      _id: v.id("customProviders"),
      name: v.string(),
      displayName: v.string(),
      baseURL: v.string(),
      models: v.array(v.string()),
      icon: v.optional(v.string()),
      description: v.optional(v.string()),
      isActive: v.boolean(),
      createdAt: v.number(),
      updatedAt: v.number(),
    })
  ),
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return [];
    }

    const providers = await ctx.db
      .query("customProviders")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .filter((q) => q.eq(q.field("isActive"), true))
      .collect();

    return providers.map((provider) => ({
      _id: provider._id,
      name: provider.name,
      displayName: provider.displayName,
      baseURL: provider.baseURL,
      models: provider.models,
      icon: provider.icon,
      description: provider.description,
      isActive: provider.isActive,
      createdAt: provider.createdAt,
      updatedAt: provider.updatedAt,
    }));
  },
});

export const get = query({
  args: {
    providerId: v.id("customProviders"),
  },
  returns: v.union(
    v.object({
      _id: v.id("customProviders"),
      name: v.string(),
      displayName: v.string(),
      baseURL: v.string(),
      apiKey: v.string(),
      models: v.array(v.string()),
      icon: v.optional(v.string()),
      description: v.optional(v.string()),
      isActive: v.boolean(),
      createdAt: v.number(),
      updatedAt: v.number(),
    }),
    v.null()
  ),
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return null;
    }

    const provider = await ctx.db.get(args.providerId);
    if (!provider || provider.userId !== userId) {
      return null;
    }

    return provider;
  },
});

export const getByName = query({
  args: {
    name: v.string(),
  },
  returns: v.union(
    v.object({
      _id: v.id("customProviders"),
      name: v.string(),
      displayName: v.string(),
      baseURL: v.string(),
      apiKey: v.string(),
      models: v.array(v.string()),
      icon: v.optional(v.string()),
      description: v.optional(v.string()),
      isActive: v.boolean(),
      createdAt: v.number(),
      updatedAt: v.number(),
    }),
    v.null()
  ),
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return null;
    }

    const provider = await ctx.db
      .query("customProviders")
      .withIndex("by_user_name", (q) =>
        q.eq("userId", userId).eq("name", args.name)
      )
      .filter((q) => q.eq(q.field("isActive"), true))
      .unique();

    if (!provider) {
      return null;
    }

    // Return only the fields specified in the validator
    return {
      _id: provider._id,
      name: provider.name,
      displayName: provider.displayName,
      baseURL: provider.baseURL,
      apiKey: provider.apiKey,
      models: provider.models,
      icon: provider.icon,
      description: provider.description,
      isActive: provider.isActive,
      createdAt: provider.createdAt,
      updatedAt: provider.updatedAt,
    };
  },
});

export const create = mutation({
  args: {
    name: v.string(),
    displayName: v.string(),
    baseURL: v.string(),
    apiKey: v.string(),
    models: v.array(v.string()),
    icon: v.optional(v.string()),
    description: v.optional(v.string()),
  },
  returns: v.id("customProviders"),
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    // Check if provider name already exists for this user
    const existing = await ctx.db
      .query("customProviders")
      .withIndex("by_user_name", (q) =>
        q.eq("userId", userId).eq("name", args.name)
      )
      .filter((q) => q.eq(q.field("isActive"), true))
      .unique();

    if (existing) {
      throw new Error("Provider with this name already exists");
    }

    const now = Date.now();
    const providerId = await ctx.db.insert("customProviders", {
      userId,
      name: args.name,
      displayName: args.displayName,
      baseURL: args.baseURL,
      apiKey: args.apiKey,
      models: args.models,
      icon: args.icon || "🤖",
      description: args.description,
      isActive: true,
      createdAt: now,
      updatedAt: now,
    });

    return providerId;
  },
});

export const update = mutation({
  args: {
    providerId: v.id("customProviders"),
    displayName: v.optional(v.string()),
    baseURL: v.optional(v.string()),
    apiKey: v.optional(v.string()),
    models: v.optional(v.array(v.string())),
    icon: v.optional(v.string()),
    description: v.optional(v.string()),
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const provider = await ctx.db.get(args.providerId);
    if (!provider || provider.userId !== userId) {
      throw new Error("Provider not found or access denied");
    }

    const updates: any = {
      updatedAt: Date.now(),
    };

    if (args.displayName !== undefined) updates.displayName = args.displayName;
    if (args.baseURL !== undefined) updates.baseURL = args.baseURL;
    if (args.apiKey !== undefined) updates.apiKey = args.apiKey;
    if (args.models !== undefined) updates.models = args.models;
    if (args.icon !== undefined) updates.icon = args.icon;
    if (args.description !== undefined) updates.description = args.description;

    await ctx.db.patch(args.providerId, updates);
    return null;
  },
});

export const remove = mutation({
  args: {
    providerId: v.id("customProviders"),
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const provider = await ctx.db.get(args.providerId);
    if (!provider || provider.userId !== userId) {
      throw new Error("Provider not found or access denied");
    }

    await ctx.db.patch(args.providerId, {
      isActive: false,
      updatedAt: Date.now(),
    });

    return null;
  },
});

export const testConnection = action({
  args: {
    baseURL: v.string(),
    apiKey: v.string(),
    model: v.optional(v.string()),
  },
  returns: v.object({
    success: v.boolean(),
    error: v.optional(v.string()),
  }),
  handler: async (ctx, args) => {
    try {
      // Test the connection by making a simple request to the provider
      const response = await fetch(`${args.baseURL}/models`, {
        method: "GET",
        headers: {
          Authorization: `Bearer ${args.apiKey}`,
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        return {
          success: false,
          error: `HTTP ${response.status}: ${response.statusText}`,
        };
      }

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  },
});
